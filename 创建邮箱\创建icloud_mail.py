import os
import sqlite3
from datetime import datetime


class EmailManager:
    """通用邮箱管理类 - 极简版"""
    
    def __init__(self):
        """初始化邮箱管理器"""
        # 数据库路径 - 桌面
        desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
        self.db_path = os.path.join(desktop_path, "apple_hide_emails.db")
        print(f"[数据库路径] {self.db_path}")
    
    def get_email(self):
        """从数据库获取一个邮箱地址
        
        Returns:
            str: 邮箱地址，如果没有可用邮箱则返回None
        """
        # 检查数据库文件是否存在
        if not os.path.exists(self.db_path):
            print(f"[错误] 数据库文件不存在: {self.db_path}")
            return None
            
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 开始事务
            conn.execute('BEGIN IMMEDIATE')
            
            # 查询第一条记录
            cursor.execute("SELECT id, email FROM emails ORDER BY id LIMIT 1")
            result = cursor.fetchone()
            
            if result:
                record_id, email_address = result
                # 删除这条记录
                cursor.execute("DELETE FROM emails WHERE id = ?", (record_id,))
                
                # 检查删除是否成功
                if cursor.rowcount > 0:
                    # 将删除的邮箱添加到"已使用"表中
                    try:
                        # 确保"已使用"表存在
                        cursor.execute('''
                            CREATE TABLE IF NOT EXISTS 已使用 (
                                id INTEGER PRIMARY KEY AUTOINCREMENT,
                                email TEXT NOT NULL UNIQUE,
                                use_time TEXT NOT NULL
                            )
                        ''')
                        
                        # 获取当前时间
                        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                        
                        # 插入到已使用表
                        cursor.execute(
                            "INSERT OR IGNORE INTO 已使用 (email, use_time) VALUES (?, ?)",
                            (email_address, current_time)
                        )
                    except Exception as e:
                        print(f"[警告] 添加到已使用表失败: {e}")
                        
                    conn.commit()
                    conn.close()
                    print(f"[成功] 获取到邮箱: {email_address}")
                    return email_address
                else:
                    conn.rollback()
                    conn.close()
                    print(f"[错误] 删除邮箱记录失败")
                    return None
            else:
                conn.rollback()
                conn.close()
                print("[警告] 数据库中没有可用的邮箱地址")
                return None
                
        except Exception as e:
            try:
                conn.rollback()
                conn.close()
            except:
                pass
            print(f"[错误] 数据库操作失败: {e}")
            return None



def main():
    # 创建邮箱管理器
    email_manager = EmailManager()
    
    # 获取邮箱
    email = email_manager.get_email()
    if email:
        print(f"获取到邮箱: {email}")
    else:
        print("无法获取邮箱")


if __name__ == "__main__":
    main() 