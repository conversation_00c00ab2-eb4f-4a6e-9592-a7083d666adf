import time
import pymysql

__all__ = ["DatabaseManager"]

class DatabaseManager:
    """数据库管理类，处理 Cursor 账号数据库操作 - 默认使用腾讯云"""

    # 固定使用腾讯云数据库配置
    DB_CONFIG = {
        "host": "**************",
        "port": 3306,
        "database": "kami3162",
        "user": "root",
        "password": "Yuyu6709.",
        "connect_timeout": 30,
        "charset": "utf8mb4"
    }

    def __init__(self):
        """初始化数据库管理器，默认使用腾讯云配置"""
        self.config = self.DB_CONFIG

    # ------------------------------------------------------------------
    # Public API
    # ------------------------------------------------------------------
    def save_account(self, email: str, access_token: str, refresh_token: str) -> bool:
        """保存账号信息到数据库，成功返回 True"""
        print(f"保存账号 {email} 到数据库…")

        for retry in range(2):  # 最多重试 2 次
            if retry:
                print("重试连接网络数据库…")

            conn = None
            cursor = None
            try:
                # 连接 MySQL
                try:
                    print(f"尝试连接网络数据库: {self.config['host']}:{self.config['port']}")
                    conn = pymysql.connect(**self.config)
                    print("网络数据库连接成功")
                except Exception as e:
                    print(f"网络数据库连接失败: {e}")
                    time.sleep(1)
                    continue

                cursor = conn.cursor()

                # 检查邮箱是否已存在
                cursor.execute("SELECT COUNT(*) FROM 邮箱系统 WHERE 邮箱 = %s", (email,))
                (cnt,) = cursor.fetchone()
                if cnt:
                    return True

                # 插入记录
                sql = (
                    "INSERT INTO 邮箱系统 (邮箱, 访问令牌, 刷新令牌, 创建时间) "
                    "VALUES (%s, %s, %s, NOW())"
                )
                cursor.execute(sql, (email, access_token, refresh_token))
                conn.commit()
                return True

            except pymysql.Error as e:
                if hasattr(e, "args") and len(e.args) > 1:
                    if e.args[0] == 1142:  # 权限错误
                        print("数据库权限错误")
                    elif e.args[0] == 1062:  # 重复键
                        return True
                if retry == 1:
                    print(f"网络数据库错误: {e}")
            except Exception as e:
                if retry == 1:
                    print(f"网络连接错误: {e}")
            finally:
                if cursor:
                    cursor.close()
                if conn:
                    conn.close()

            time.sleep(1)
        return False 