

import random
import re
import string
import time
import tkinter as tk
from datetime import datetime
from tkinter import ttk, messagebox, scrolledtext
import requests
from bs4 import BeautifulSoup
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

class TempEmailManager:
    """临时邮箱管理及验证码获取类"""
    
    def __init__(self, domain=None):
        """初始化邮箱管理器"""
        # 加载配置中的域名，如果没有提供
        if domain is None:
            config = load_config()
            self.domain = config.get("temp_email_domain")
            # 确保一定有域名
            if not self.domain:
                raise ValueError("未设置临时邮箱域名，请在配置中指定")
        else:
            self.domain = domain
            
        self.username = None
        self.email = None
        print(f"使用临时邮箱域名: {self.domain}")
        
    def generate_random_username(self, length=10):
        """生成随机用户名"""
        letters = string.ascii_lowercase
        return ''.join(random.choice(letters) for i in range(length))
    
    def get_temp_email(self):
        """获取临时邮箱"""
        print("\n===== 临时邮箱获取 =====")
        
        # 生成随机用户名
        self.username = self.generate_random_username()
        self.email = f"{self.username}@{self.domain}"
        
        print(f"生成临时邮箱: {self.email}")
        return self.email
    
    def get_verification_code(self, timeout=120):
        """从临时邮箱获取验证码"""
        print("\n===== 临时邮箱验证码接收 =====")
        
        if not self.username or not self.email:
            print("错误: 未设置邮箱")
            return None
        
        # 构建URL
        url = f"https://mail-temp.com/{self.domain}/{self.username}"
        print(f"邮箱URL: {url}")
        
        # 禁用SSL警告
        import urllib3
        urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
        
        print(f"开始获取邮箱 {self.email} 的验证码...")
        
        # 最大重试次数改为8次
        max_attempts = 8  # 减少最大尝试次数
        attempt = 0
        
        # 创建具有重试机制的会话
        session = requests.Session()
        retry_strategy = Retry(
            total=3,
            status_forcelist=[429, 500, 502, 503, 504],
            allowed_methods=["HEAD", "GET", "OPTIONS"],
            backoff_factor=0.5  # 减少重试等待时间
        )
        adapter = HTTPAdapter(max_retries=retry_strategy)
        session.mount("http://", adapter)
        session.mount("https://", adapter)
        
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache',
            'Referer': 'https://mail-temp.com/'
        }
        
        while attempt < max_attempts:
            try:
                # 减少随机延迟
                time.sleep(1)
                
                # 获取页面内容，增加超时设置
                response = session.get(url, headers=headers, timeout=15, verify=False)
                response.raise_for_status()
                
                # 解析HTML
                soup = BeautifulSoup(response.text, 'html.parser')

                # 提取页面的纯文本内容
                page_text = soup.get_text()

                # 打印解码后的内容（用于调试）
                print(f"页面内容预览: {page_text[:200]}...")

                # 检查是否有邮件内容
                if page_text and len(page_text.strip()) > 100:  # 确保有实际内容
                    print("找到邮件内容，开始解析...")

                    # 多种验证码模式匹配
                    verification_patterns = [
                        r'Your verification code is (\d{6})',  # Cursor标准格式
                        r'verification code[:\s]+(\d{6})',     # 通用格式1
                        r'code[:\s]+(\d{6})',                  # 通用格式2
                        r'(\d{6})',                            # 任意6位数字
                    ]

                    for pattern in verification_patterns:
                        code_match = re.search(pattern, page_text, re.IGNORECASE)
                        if code_match:
                            code = code_match.group(1)
                            print(f"✅ 使用模式 '{pattern}' 找到验证码: {code}")
                            return code

                    # 如果没找到验证码，打印更多内容用于调试
                    print("未找到验证码，邮件内容:")
                    print("-" * 50)
                    print(page_text[:500])  # 打印前500个字符
                    print("-" * 50)
                else:
                    print("页面内容为空或过短，可能还没有邮件")
                
                attempt += 1
                if attempt < max_attempts:
                    wait_time = 2  # 固定等待时间，减少延迟
                    print(f"尝试 {attempt}/{max_attempts}: 未找到验证码，等待{wait_time}秒后重试...")
                    time.sleep(wait_time)
            
            except requests.exceptions.RequestException as e:
                print(f"网络请求错误: {e}")
                attempt += 1
                if attempt < max_attempts:
                    wait_time = 3
                    print(f"尝试 {attempt}/{max_attempts}: 网络请求失败，等待{wait_time}秒后重试...")
                    time.sleep(wait_time)
            except Exception as e:
                print(f"解析页面时出错: {e}")
                print(f"响应状态码: {response.status_code if 'response' in locals() else '未知'}")
                if 'response' in locals():
                    print(f"响应内容长度: {len(response.text)}")
                    print(f"响应内容预览: {response.text[:200]}...")
                attempt += 1
                if attempt < max_attempts:
                    wait_time = 3
                    print(f"尝试 {attempt}/{max_attempts}: 解析失败，等待{wait_time}秒后重试...")
                    time.sleep(wait_time)
        
        print("达到最大尝试次数，未找到验证码")
        return None


def load_config():
    """临时配置函数，避免依赖外部配置文件"""
    return {"temp_email_domain": "forummaxai.com"}


if __name__ == "__main__":
    # 测试用例
    print("开始测试临时邮箱管理器...")

    # 测试邮箱地址: 
    test_email = "<EMAIL>"

    # 从测试邮箱地址中提取用户名和域名
    username, domain = test_email.split("@")
    print(f"测试邮箱: {test_email}")
    print(f"用户名: {username}")
    print(f"域名: {domain}")

    try:
        # 创建邮箱管理器实例，使用指定的域名
        email_manager = TempEmailManager(domain=domain)

        # 手动设置用户名和邮箱
        email_manager.username = username
        email_manager.email = test_email

        print(f"\n设置测试邮箱: {email_manager.email}")

        # 尝试获取验证码
        print("\n开始获取验证码...")
        verification_code = email_manager.get_verification_code(timeout=60)

        if verification_code:
            print(f"\n✅ 成功获取验证码: {verification_code}")
        else:
            print("\n❌ 未能获取到验证码")

    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

    print("\n测试完成！")