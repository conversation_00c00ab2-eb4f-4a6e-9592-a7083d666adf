import os
import sys
import threading
import logging
from DrissionPage import ChromiumOptions, Chromium
from win32api import GetSystemMetrics

__all__ = ["BrowserManager"]

class BrowserManager:
    """浏览器管理器类，用于管理 Chrome 浏览器实例"""

    # 类级别计数器与锁，用于窗口定位
    window_count = 0
    window_lock = threading.Lock()
    total_threads = 0  # 总线程数，用于动态调整窗口排列

    def __init__(self):
        self.browser = None
        # 为每个实例分配唯一窗口 ID
        with BrowserManager.window_lock:
            self.window_id = BrowserManager.window_count
            BrowserManager.window_count = (BrowserManager.window_count + 1) % 20  # 最多 20 个窗口

    # ------------------------------------------------------------------
    # Public API
    # ------------------------------------------------------------------
    def init_browser(self, user_agent: str | None = None):
        """初始化浏览器并返回 DrissionPage Chromium 对象"""
        co = self._get_browser_options(user_agent)
        self.browser = Chromium(co)
        return self.browser

    def quit(self):
        """关闭浏览器实例"""
        if self.browser:
            try:
                self.browser.quit()
            except Exception:
                pass

    # ------------------------------------------------------------------
    # Internal helpers
    # ------------------------------------------------------------------
    def _get_browser_options(self, user_agent: str | None = None) -> ChromiumOptions:
        co = ChromiumOptions()

        # 加载 turnstilePatch 扩展（若存在）
        try:
            extension_path = self._get_extension_path()
            co.add_extension(extension_path)
        except FileNotFoundError as e:
            logging.warning(f"警告: {e}")

        # 为每个实例设置不同的用户配置文件
        profile_name = f"Profile_{self.window_id}"
        co.set_user(user=profile_name)

        # 基础启动配置
        co.set_pref("credentials_enable_service", False)
        co.set_argument("--hide-crash-restore-bubble")
        proxy = os.getenv("BROWSER_PROXY")
        if proxy:
            co.set_proxy(proxy)
        co.auto_port()
        if user_agent:
            co.set_user_agent(user_agent)

        # 窗口尺寸与位置
        try:
            screen_width = GetSystemMetrics(0)
            screen_height = GetSystemMetrics(1)
            max_columns = 5
            window_width = min(600, screen_width // max_columns)
            window_height = min(600, screen_height // 2)
            row, col = divmod(self.window_id, max_columns)
            x_pos = col * window_width
            y_pos = row * window_height
            co.set_argument(f"--window-size={window_width},{window_height}")
            co.set_argument(f"--window-position={x_pos},{y_pos}")
        except Exception:
            co.set_argument("--window-size=600,600")
            co.set_argument("--window-position=0,0")

        # 启用 br 压缩编码支持
        co.set_pref("profile.default_content_encoding_list", ["br", "gzip", "deflate"])
        co.set_argument("--enable-features=BrotliEncoding")
        co.set_pref("webkit.webprefs.default_encoding", "UTF-8")

        # macOS 兼容
        if sys.platform == "darwin":
            co.set_argument("--no-sandbox")
            co.set_argument("--disable-gpu")

        return co

    def _get_extension_path(self) -> str:
        root_dir = os.getcwd()
        extension_path = os.path.join(root_dir, "turnstilePatch")
        if hasattr(sys, "_MEIPASS"):
            extension_path = os.path.join(sys._MEIPASS, "turnstilePatch")
        if not os.path.exists(extension_path):
            raise FileNotFoundError(f"插件不存在: {extension_path}")
        return extension_path 